/* 主样式文件 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.container-fluid {
    min-height: calc(100vh - 56px); /* 减去导航栏高度 */
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

#connection-status {
    font-size: 0.9rem;
}

/* 左侧面板样式 */
.col-md-4.bg-light {
    min-height: calc(100vh - 116px); /* 减去导航栏和状态栏高度 */
    overflow-y: auto;
    padding-bottom: 20px;
}

/* 右侧主要内容区域 */
.col-md-8 {
    min-height: calc(100vh - 116px); /* 减去导航栏和状态栏高度 */
    overflow-y: auto;
    padding-bottom: 20px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 表单控件样式 */
.form-control-sm {
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 选项卡样式 */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom: 2px solid #0d6efd;
    background-color: transparent;
}

.nav-tabs .nav-link:hover {
    color: #0d6efd;
    border-color: transparent;
}

/* 日志容器样式 */
#log-container {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
}

.log-info {
    color: #17a2b8;
}

.log-success {
    color: #28a745;
}

.log-warning {
    color: #ffc107;
}

.log-error {
    color: #dc3545;
}

/* 进度条样式 */
.progress {
    height: 1.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    font-size: 0.875rem;
    font-weight: 500;
}

/* 图片预览样式 */
.image-preview {
    position: relative;
    display: inline-block;
    margin: 0.5rem;
    border: 2px solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview:hover {
    border-color: #0d6efd;
    transform: scale(1.05);
}

.image-preview.selected {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.image-preview img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    display: block;
}

.image-preview .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview:hover .overlay {
    opacity: 1;
}

.image-preview.selected .overlay {
    opacity: 1;
    background-color: rgba(40, 167, 69, 0.8);
}

/* 搜索结果样式 */
.search-result-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
}

.search-result-header {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.search-result-stats {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-connected {
    background-color: #28a745;
}

.status-disconnected {
    background-color: #dc3545;
}

.status-connecting {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 底部状态栏 */
.fixed-bottom {
    z-index: 1020;
    height: 50px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container-fluid {
        min-height: auto;
    }

    .col-md-4.bg-light,
    .col-md-8 {
        min-height: auto;
        padding-bottom: 1rem;
    }
    
    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .image-preview img {
        width: 120px;
        height: 120px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}
